package vinsf.miniapp.dto;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import vinsf.miniapp.constant.MiniAppPermission;
import vinsf.miniapp.constant.MiniAppStatus;
import vinsf.miniapp.constant.MiniAppType;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Bean validation tests for MiniAppDto custom annotations and constraints.
 *
 * Naming convention: methodName_condition_expectedResult
 */
class MiniAppDtoValidationTest {

    private static ValidatorFactory factory;
    private static Validator validator;

    @BeforeAll
    static void setUpValidator() {
        factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @AfterAll
    static void tearDownValidator() {
        factory.close();
    }

    private MiniAppDto buildValidDto() {
        Map<String, String> displayNames = Map.of("en", "Test App");
        Map<String, String> descriptions = Map.of(
                "en", "This is a valid description with enough length."
        );
        Map<String, String> logos = Map.of(
                "en", "https://example.com/logo.png"
        );
        Set<String> showcase = new LinkedHashSet<>(Arrays.asList(
                "https://example.com/s1.png",
                "https://example.com/s2.jpg"
        ));
        Set<String> whitelistIps = new LinkedHashSet<>(Arrays.asList(
                "***********",
                "2001:0db8:85a3:0000:0000:8a2e:0370:7334"
        ));
        Set<String> whitelistDomains = new LinkedHashSet<>(Arrays.asList(
                "example.com",
                "sub.example.org"
        ));

        return new MiniAppDto(
                "id-1",
                "code-1",
                "Name",
                new EncryptedSecret("ciph", "iv", "edk", "keyId", "1234"),
                10L,
                100L,
                MiniAppType.WEB,
                displayNames,
                descriptions,
                logos,
                null, // banners optional
                showcase,
                Set.of(), // scopes
                Set.of(MiniAppPermission.USER_PROFILE),
                whitelistIps,
                whitelistDomains,
                Set.of(),
                Map.of(),
                MiniAppStatus.ACTIVE,
                1, "creator", "updater", LocalDateTime.now().minusDays(1), LocalDateTime.now(), true
        );
    }

    @Test
    void validate_validDto_noViolations() {
        MiniAppDto dto = buildValidDto();
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), () -> "Unexpected violations: " + violations);
    }

    @Test
    void validate_codeBlank_returnsViolation() {
        MiniAppDto dto = new MiniAppDto(
                "id",
                " ",
                "Name",
                new EncryptedSecret("ciph", "iv", "edk", "keyId", "1234"),
                1L,
                null,
                MiniAppType.WEB,
                Map.of("en", "Name"),
                Map.of("en", "This is a valid description text"),
                Map.of("en", "https://example.com/logo.png"),
                null,
                Set.of(),
                Set.of(),
                Set.of(),
                Set.of("example.com"),
                Set.of(),
                Set.of(),
                Map.of(),
                MiniAppStatus.ACTIVE,
                null,null,null,null,null,true
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("code") && v.getMessage().equals("Code is required")));
    }

    @Test
    void validate_codeTooLong_returnsViolation() {
        String longCode = "c".repeat(65);
        MiniAppDto dto = buildValidDto();
        dto = new MiniAppDto(
                dto.id(), longCode, dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), dto.logos(), dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("code") && v.getMessage().contains("must not exceed 64")));
    }

    @Test
    void validate_nameBlank_returnsViolation() {
        MiniAppDto dto = buildValidDto();
        dto = new MiniAppDto(
                dto.id(), dto.code(), "", dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), dto.logos(), dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("name") && v.getMessage().equals("Name is required")));
    }

    @Test
    void validate_nameTooLong_returnsViolation() {
        MiniAppDto dto = buildValidDto();
        String longName = "n".repeat(101);
        dto = new MiniAppDto(
                dto.id(), dto.code(), longName, dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), dto.logos(), dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("name") && v.getMessage().contains("must not exceed 100")));
    }

    @Test
    void validate_requiredFieldsNull_returnsViolations() {
        MiniAppDto dto = new MiniAppDto(
                null,
                "code",
                "Name",
                null, // secretEncrypted
                null, // categoryId
                null, // orgCode
                null, // type
                null, // displayNames
                null, // descriptions
                null, // logos
                null, // banners
                null, // showcase
                null, // scopes
                null, // permissions
                null, // whitelistIps
                null, // whitelistDomains
                null, // webhooks
                null, // keys
                null, // status
                null, null, null, null, null, null
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        // Expect at least the following messages
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("Secret is required")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("Category ID is required")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("Type is required")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("Display names are required")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("Descriptions are required")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("Logos are required")));
        assertTrue(violations.stream().anyMatch(v -> v.getMessage().equals("Status is required")));
    }

    @Test
    void validate_displayNamesEmpty_returnsViolation() {
        MiniAppDto dto = buildValidDto();
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                Collections.emptyMap(), dto.descriptions(), dto.logos(), dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("displayNames") && v.getMessage().contains("must not be empty")));
    }

    @Test
    void validate_descriptionsTooShort_returnsViolationMessage() {
        MiniAppDto dto = buildValidDto();
        Map<String, String> tooShort = Map.of("en", "too short"); // length 9, min 10
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), tooShort, dto.logos(), dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        // The validator composes a detailed message including "too short"
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("descriptions") && v.getMessage().contains("too short")));
    }

    @Test
    void validate_logosInvalidUrl_returnsViolationMessage() {
        MiniAppDto dto = buildValidDto();
        Map<String, String> badLogos = Map.of("en", "not-a-url");
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), badLogos, dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("logos") && v.getMessage().contains("Invalid URL format")));
    }

    @Test
    void validate_bannersNull_allowed() {
        MiniAppDto dto = buildValidDto();
        assertNull(dto.banners());
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), () -> violations.toString());
    }

    @Test
    void validate_showcaseExceedsMax_returnsViolation() {
        MiniAppDto dto = buildValidDto();
        Set<String> bigShowcase = new LinkedHashSet<>();
        for (int i = 0; i < 11; i++) {
            bigShowcase.add("https://example.com/" + i + ".png");
        }
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), dto.logos(), dto.banners(), bigShowcase, dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("showcase") && v.getMessage().contains("must not exceed 10")));
    }

    @Test
    void validate_showcaseInvalidUrl_returnsViolationMessage() {
        MiniAppDto dto = buildValidDto();
        Set<String> showcase = new LinkedHashSet<>(Arrays.asList("https://example.com/s1.png", "bad://url"));
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), dto.logos(), dto.banners(), showcase, dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("showcase") && v.getMessage().contains("Invalid image URL")));
    }

    @Test
    void validate_whitelistIpsInvalid_returnsViolationMessage() {
        MiniAppDto dto = buildValidDto();
        Set<String> ips = new LinkedHashSet<>(Arrays.asList("***********", "999.999.999.999"));
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), dto.logos(), dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                ips, dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        // Validator crafts message with "Invalid IP address or CIDR notation"
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("whitelistIps") && v.getMessage().contains("Invalid IP address or CIDR notation")));
    }

    @Test
    void validate_whitelistDomainsInvalid_returnsViolationMessage() {
        MiniAppDto dto = buildValidDto();
        Set<String> domains = new LinkedHashSet<>(Arrays.asList("example.com", "-bad-.com"));
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                dto.displayNames(), dto.descriptions(), dto.logos(), dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), domains, dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        assertTrue(violations.stream().anyMatch(v -> v.getPropertyPath().toString().contains("whitelistDomains") && v.getMessage().contains("Invalid domain name")));
    }

    @Test
    void validate_mapsAllowNullValues_edgeCaseValid() {
        // Validators skip null values; map non-empty to satisfy @Size(min=1)
        MiniAppDto dto = buildValidDto();
        Map<String, String> displayNames = new HashMap<>();
        displayNames.put("en", null);
        Map<String, String> descriptions = new HashMap<>();
        descriptions.put("en", null);
        Map<String, String> logos = new HashMap<>();
        logos.put("en", null);
        dto = new MiniAppDto(
                dto.id(), dto.code(), dto.name(), dto.secretEncrypted(), dto.categoryId(), dto.orgCode(), dto.type(),
                displayNames, descriptions, logos, dto.banners(), dto.showcase(), dto.scopes(), dto.permissions(),
                dto.whitelistIps(), dto.whitelistDomains(), dto.webhooks(), dto.keys(), dto.status(), dto.version(), dto.createdBy(), dto.updatedBy(), dto.createdOn(), dto.updatedOn(), dto.active()
        );
        Set<ConstraintViolation<MiniAppDto>> violations = validator.validate(dto);
        // No violations from custom validators; only @NotBlank/@Size do not apply to values here
        assertTrue(violations.isEmpty(), () -> violations.toString());
    }
}
