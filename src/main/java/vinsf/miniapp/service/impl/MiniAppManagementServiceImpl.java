package vinsf.miniapp.service.impl;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vinsf.miniapp.constant.AppErrorCode;
import vinsf.miniapp.constant.KeyType;
import vinsf.miniapp.constant.MiniAppStatus;
import vinsf.miniapp.dto.*;
import vinsf.miniapp.exception.BusinessLogicException;
import vinsf.miniapp.exception.InvalidStatusException;
import vinsf.miniapp.mapper.MiniAppMapper;
import vinsf.miniapp.service.KmsService;
import vinsf.miniapp.service.MiniAppManagementService;
import vinsf.miniapp.service.MiniAppService;
import vinsf.miniapp.util.CryptoUtil;

import java.security.KeyPair;
import java.security.interfaces.RSAPublicKey;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Business Service implementation for MiniApp Management operations
 *
 * <AUTHOR>
 * @since 2025-10-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MiniAppManagementServiceImpl implements MiniAppManagementService {
    private static final int DEFAULT_SECRET_LENGTH = 32; // 256-bit
    private static final int DEFAULT_SIGNING_KEY_SIZE = 2048;
    private static final String DEFAULT_SIGNING_KEY_ALGORITHM = "RSA";
    private static final int DEFAULT_SIGNING_KEY_ROTATE_DAYS = 90;

    private final MiniAppService miniAppService;
    private final MiniAppMapper miniAppMapper;
    private final KmsService kmsService;

    @Override
    @Transactional
    public MiniAppDto create(@Valid MiniAppCreateDto dto) {
        // Generate and encrypt secret
        String secret = CryptoUtil.generateSecret(DEFAULT_SECRET_LENGTH);
        EncryptedSecret encryptedSecret = kmsService.encrypt(secret);

        // Generate signing key
        MiniAppKey signingKey = generateSigningKey();

        // Build keys map
        Map<KeyType, Set<MiniAppKey>> keys = new HashMap<>();
        keys.put(KeyType.SIGNING, Set.of(signingKey));

        return miniAppService.create(miniAppMapper.toDto(
                dto,
                encryptedSecret,
                keys
        ));
    }

    @Override
    @Transactional
    public MiniAppDto update(String id, @Valid MiniAppUpdateDto dto) {
        return miniAppService.updatePartial(id, miniAppMapper.toDto(dto));
    }

    @Override
    @Transactional
    public void delete(String id) {
        var miniApp = miniAppService.getById(id);
        if (!MiniAppStatus.REGISTERED.equals(miniApp.status())) {
            log.warn("Cannot delete MiniApp not in status REGISTERED: {}", id);
            throw new InvalidStatusException("MiniApp", new Object[]{MiniAppStatus.REGISTERED}, miniApp.status());
        }
        miniAppService.delete(id);
    }

    @Override
    @Transactional
    public MiniAppDto changeStatus(String id, String status) {
        var miniApp = miniAppService.getById(id);
        var oldStatus = miniApp.status();
        var newStatus = MiniAppStatus.fromCode(status);

        if (MiniAppStatus.ACTIVE.equals(newStatus)) {
            if (!MiniAppStatus.INACTIVE.equals(oldStatus)) {
                log.warn("Cannot change status to ACTIVE for MiniApp not in status INACTIVE : {}", id);
                throw new InvalidStatusException("Current MiniApp", new Object[]{MiniAppStatus.INACTIVE}, oldStatus);

                // TODO: Check miniapp must have at least 1 active release version
            }
        } else if (MiniAppStatus.INACTIVE.equals(newStatus)) {
            if (!MiniAppStatus.ACTIVE.equals(oldStatus)) {
                log.warn("Cannot change status to INACTIVE for MiniApp not in status ACTIVE : {}", id);
                throw new InvalidStatusException("Current MiniApp", new Object[]{MiniAppStatus.ACTIVE}, oldStatus);
            }
        } else {
            log.warn("Cannot change status to {} for MiniApp: {}", newStatus, id);
            throw new InvalidStatusException("New MiniApp", new Object[]{MiniAppStatus.ACTIVE, MiniAppStatus.INACTIVE}, newStatus);
        }

        return miniAppService.updatePartial(id,
                MiniAppDto.builder()
                        .status(newStatus)
                        .build()
        );
    }

    @Override
    @Transactional(readOnly = true)
    public MiniAppDto getById(String id) {
        return miniAppService.getById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MiniAppDto> search(MiniAppFilter filter, Pageable pageable) {
        return miniAppService.search(filter, pageable);
    }

    @Override
    @Transactional(readOnly = true)
    public String retrieveSecret(String id) {
        log.info("Retrieving secret for MiniApp id: {}", id);
        var miniApp = miniAppService.getById(id);
        return kmsService.decrypt(miniApp.secretEncrypted());
    }

    @Override
    @Transactional
    public MiniAppDto regenerateSecret(String id) {
        log.info("Regenerating secret for MiniApp id: {}", id);

        // Check if MiniApp is active
        var miniApp = miniAppService.getById(id);
        if (!MiniAppStatus.ACTIVE.equals(miniApp.status())) {
            log.warn("Cannot regenerate secret for active MiniApp id: {}", id);
            throw new BusinessLogicException(
                    ServiceResponse.error(AppErrorCode.MINIAPP_CANNOT_REGENERATE_SECRET_ACTIVE)
            );
        }

        // Generate new secret and encrypt
        String newSecret = CryptoUtil.generateSecret(DEFAULT_SECRET_LENGTH);
        EncryptedSecret newEncryptedSecret = kmsService.encrypt(newSecret);

        MiniAppDto updated = miniAppService.updatePartial(id,
                MiniAppDto.builder()
                        .secretEncrypted(newEncryptedSecret)
                        .build()
        );

        log.info("Secret regenerated successfully for MiniApp id: {}", id);

        return updated;
    }

    @Override
    @Transactional
    public MiniAppDto rotateSigningKey(String id) {
        log.info("Rotating signing key for MiniApp id: {}", id);

        // Generate new RSA-2048 keypair
        MiniAppKey newSigningKey = generateSigningKey();

        // Deactivate old signing key
        MiniAppDto miniApp = miniAppService.getById(id);
        Set<MiniAppKey> signingKeys = miniApp.keys().get(KeyType.SIGNING).stream()
                .map(key -> key.toBuilder().active(false).build())
                .collect(Collectors.toSet());
        signingKeys.add(newSigningKey);

        // Update MiniApp keys
        MiniAppDto updated = miniAppService.updatePartial(id,
                MiniAppDto.builder()
                        .keys(Map.of(KeyType.SIGNING, signingKeys))
                        .build()
        );

        log.info("Signing key rotated successfully for MiniApp id: {}", id);

        return updated;
    }

    /**
     * Generates a new RSA-2048 keypair and encrypts the private key using AWS KMS
     *
     * @return MiniAppKey containing public and encrypted private key
     */
    private MiniAppKey generateSigningKey() {
        KeyPair signingKeyPair = CryptoUtil.generateKeypair(DEFAULT_SIGNING_KEY_ALGORITHM, DEFAULT_SIGNING_KEY_SIZE);
        String publicKey = CryptoUtil.encodePublicKey(signingKeyPair.getPublic());
        String privateKey = CryptoUtil.encodePrivateKey(signingKeyPair.getPrivate());
        EncryptedSecret encryptedPrivateKey = kmsService.encrypt(privateKey);

        // Create signing key
        String kid = CryptoUtil.generateRsaKeyId((RSAPublicKey) signingKeyPair.getPublic());
        return MiniAppKey.builder()
                .kid(kid)
                .alg(DEFAULT_SIGNING_KEY_ALGORITHM)
                .publicKey(publicKey)
                .privateKeyEncrypted(encryptedPrivateKey)
                .rotateAt(System.currentTimeMillis() + Duration.ofDays(DEFAULT_SIGNING_KEY_ROTATE_DAYS).toMillis())
                .active(true)
                .build();
    }
}
