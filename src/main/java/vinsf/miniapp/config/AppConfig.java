package vinsf.miniapp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver;
import vinsf.miniapp.constant.AppConst;

import java.util.Locale;
import java.util.Optional;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class AppConfig {

    @Bean
    public LocaleResolver localeResolver() {
        AcceptHeaderLocaleResolver resolver = new AcceptHeaderLocaleResolver();
        resolver.setDefaultLocale(Locale.of(AppConst.DEFAULT_LANGUAGE, AppConst.DEFAULT_COUNTRY));
        return resolver;
    }

    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            if (SecurityContextHolder.getContext().getAuthentication() == null) {
                return Optional.of(AppConst.UPDATED_BY_AUTO);
            }

            return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication().getName());
        };
    }

}
