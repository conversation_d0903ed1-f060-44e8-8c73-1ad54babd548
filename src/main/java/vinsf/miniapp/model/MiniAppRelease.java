package vinsf.miniapp.model;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import vinsf.miniapp.constant.ReleaseChannel;
import vinsf.miniapp.constant.ReleaseStatus;
import vinsf.miniapp.model.base.BaseEntityLong;

import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Entity
@Table(name = "miniapp_releases")
public class MiniAppRelease extends BaseEntityLong {

    @Column(name = "miniapp_id", nullable = false)
    private String miniappId;

    @Column(name = "bundle_id", nullable = false)
    private Long bundleId;

    @Column(name = "channel", length = 8, nullable = false)
    private ReleaseChannel channel;

    @Column(name = "status", length = 16)
    private ReleaseStatus status;

    @Column(name = "rollout_percentage")
    private int rolloutPercentage;

    @Column(name = "releaser", length = 100)
    private String releaser;

    @Column(name = "released_at")
    private Long releasedAt;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "release_notes", columnDefinition = "jsonb")
    private Map<String, String> releaseNotes; // {"vi": "...", "en":"..."}

}
