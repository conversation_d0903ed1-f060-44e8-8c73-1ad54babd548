---
type: "always_apply"
---

# Custom Validation Patterns

## Custom Validation Annotations

The codebase uses extensive custom validation annotations for domain-specific validation rules. All custom validators follow Jakarta Bean Validation patterns.

### IP Address Validation
- **@ValidateIPs**: Validates single IP addresses (IPv4, IPv6, CIDR notation)
- **@ValidateIPsCollection**: Validates collections of IP addresses
- **Implementation**: `IPsValidator` and `IPsCollectionValidator`

```java
@ValidateIPs(message = "Whitelist IPs must be valid IPv4, IPv6, or CIDR notation")
Set<String> whitelistIps;
```

### Domain Validation
- **@ValidateDomains**: Validates single domain names
- **@ValidateDomainsCollection**: Validates collections of domain names
- **Implementation**: `DomainsValidator` and `DomainsCollectionValidator`

```java
@ValidateDomains(message = "Whitelist domains must be valid domain names")
Set<String> whitelistDomains;
```

### Image URL Validation
- **@ValidateImageUrls**: Validates image URLs with supported types
- **@ValidateImageUrlsCollection**: Validates collections of image URLs
- **Supported types**: PNG, JPG, JPEG, WEBP
- **Implementation**: `ImageUrlsValidator` and `ImageUrlsCollectionValidator`

```java
@ValidateImageUrls(supportedTypes = {ImageType.PNG, ImageType.JPG, ImageType.JPEG}, 
                   message = "Invalid showcase image URL")
Set<String> showcase;
```

### Locale Map Validation
- **@ValidateTextLocaleMap**: Validates text content in locale maps
- **@ValidateImageLocaleMap**: Validates image URLs in locale maps
- **Purpose**: Ensures proper internationalization content

```java
@ValidateTextLocaleMap(message = "Display names must be valid locale map")
Map<String, String> displayNames;

@ValidateImageLocaleMap(message = "Logos must be valid image locale map")
Map<String, String> logos;
```

## Validation Patterns

### Validator Implementation Pattern
```java
@Component
public class CustomValidator implements ConstraintValidator<CustomAnnotation, String> {
    
    @Override
    public void initialize(CustomAnnotation annotation) {
        // Initialize validator with annotation parameters
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // Null values are typically considered valid (use @NotNull separately)
        if (value == null) {
            return true;
        }
        
        // Implement validation logic
        return validateLogic(value);
    }
    
    private boolean validateLogic(String value) {
        // Custom validation implementation
        return true;
    }
}
```

### Collection Validator Pattern
```java
@Component
public class CustomCollectionValidator implements ConstraintValidator<CustomAnnotation, Collection<String>> {
    
    @Override
    public boolean isValid(Collection<String> values, ConstraintValidatorContext context) {
        if (values == null || values.isEmpty()) {
            return true;
        }
        
        // Validate each item in collection
        for (String value : values) {
            if (!validateSingleItem(value)) {
                return false;
            }
        }
        return true;
    }
}
```

## Validation Annotation Patterns

### Basic Annotation Structure
```java
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CustomValidator.class)
@Documented
public @interface ValidateCustom {
    String message() default "Invalid value";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
    
    // Custom parameters
    CustomType[] supportedTypes() default {};
}
```

### Parameterized Validation
```java
// Annotation with parameters
@ValidateImageUrls(
    supportedTypes = {ImageType.PNG, ImageType.JPG}, 
    message = "Only PNG and JPG images are allowed"
)
Set<String> imageUrls;
```

## Testing Custom Validators

### Unit Test Pattern
```java
@ExtendWith(MockitoExtension.class)
class CustomValidatorTest {
    
    @Mock
    private ConstraintValidatorContext context;
    
    @Mock
    private ConstraintValidatorContext.ConstraintViolationBuilder violationBuilder;
    
    private CustomValidator validator;
    
    @BeforeEach
    void setUp() {
        validator = new CustomValidator();
        // Setup mock behavior for constraint violations
        lenient().when(context.buildConstraintViolationWithTemplate(anyString()))
                .thenReturn(violationBuilder);
    }
    
    @Test
    void isValid_nullValue_returnsTrue() {
        assertTrue(validator.isValid(null, context));
    }
    
    @Test
    void isValid_validValue_returnsTrue() {
        assertTrue(validator.isValid("valid-value", context));
    }
    
    @Test
    void isValid_invalidValue_returnsFalse() {
        assertFalse(validator.isValid("invalid-value", context));
    }
}
```

### DTO Validation Testing
```java
@ExtendWith(MockitoExtension.class)
class DtoValidationTest {
    
    private Validator validator;
    
    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }
    
    @Test
    void validate_validDto_noViolations() {
        MyDto dto = buildValidDto();
        Set<ConstraintViolation<MyDto>> violations = validator.validate(dto);
        assertTrue(violations.isEmpty(), () -> "Unexpected violations: " + violations);
    }
    
    @Test
    void validate_invalidField_hasViolations() {
        MyDto dto = buildInvalidDto();
        Set<ConstraintViolation<MyDto>> violations = validator.validate(dto);
        assertFalse(violations.isEmpty());
        
        // Verify specific violation
        ConstraintViolation<MyDto> violation = violations.iterator().next();
        assertEquals("fieldName", violation.getPropertyPath().toString());
        assertEquals("Expected error message", violation.getMessage());
    }
}
```

## Best Practices

1. **Null Handling**: Always return `true` for null values in validators (use `@NotNull` separately)
2. **Error Messages**: Provide clear, actionable error messages
3. **Performance**: Keep validation logic lightweight and efficient
4. **Reusability**: Create separate validators for single items and collections
5. **Testing**: Write comprehensive unit tests for all validation scenarios
6. **Documentation**: Document supported formats and edge cases in validator comments
7. **Parameterization**: Use annotation parameters for configurable validation rules
8. **Separation of Concerns**: Keep validation logic separate from business logic
