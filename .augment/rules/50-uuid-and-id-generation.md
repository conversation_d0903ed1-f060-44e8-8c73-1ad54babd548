---
type: "always_apply"
---

# UUID and ID Generation Patterns

## UUIDv7 Generation

The codebase uses UUIDv7 for primary key generation, which provides better database performance compared to UUIDv4 due to its time-ordered nature.

### Custom UUIDv7 Annotation
```java
@IdGeneratorType(UUIDv7Generator.class)
@Target({FIELD, METHOD})
@Retention(RUNTIME)
public @interface UUIDv7Generated {}
```

### Entity ID Pattern
```java
@MappedSuperclass
@Getter
@Setter
@ToString
@FieldNameConstants
public abstract class BaseEntityUUID extends BaseEntity {
    
    @Id
    @UUIDv7Generated
    private String id;
}
```

### Usage in Entities
```java
@Entity
@Table(name = "miniapps")
@Data
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class MiniApp extends BaseEntityUUID {
    // Entity fields...
}
```

## Base Entity Hierarchy

### BaseEntity (Abstract)
Provides common audit fields for all entities:
- `version` (Integer) - Optimistic locking
- `createdBy` (String) - Audit: who created
- `updatedBy` (String) - Audit: who last updated
- `createdOn` (LocalDateTime) - Audit: when created
- `updatedOn` (LocalDateTime) - Audit: when last updated
- `active` (Boolean) - Soft delete flag

```java
@MappedSuperclass
@Getter
@Setter
@ToString
@FieldNameConstants
@EntityListeners(AuditingEntityListener.class)
public abstract class BaseEntity implements Serializable {
    
    @Version
    private Integer version;
    
    @CreatedBy
    @Column(name = "created_by", updatable = false)
    private String createdBy;
    
    @LastModifiedBy
    @Column(name = "updated_by")
    private String updatedBy;
    
    @CreatedDate
    @Column(name = "created_on", updatable = false, columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime createdOn;
    
    @LastModifiedDate
    @Column(name = "updated_on", columnDefinition = "TIMESTAMPTZ")
    private LocalDateTime updatedOn;
    
    @Column(name = "active", columnDefinition = "boolean default true")
    private Boolean active;
}
```

### BaseEntityUUID
For entities using UUID primary keys (recommended):
```java
@MappedSuperclass
public abstract class BaseEntityUUID extends BaseEntity {
    
    @Id
    @UUIDv7Generated
    private String id;
}
```

### BaseEntityLong
For entities using Long primary keys (legacy or specific use cases):
```java
@MappedSuperclass
public abstract class BaseEntityLong extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
}
```

## ID Generation Best Practices

### When to Use UUIDv7
- **New entities**: Always prefer UUIDv7 for new entity designs
- **Distributed systems**: Better for microservices and distributed architectures
- **Database performance**: Time-ordered nature improves index performance
- **Security**: No predictable sequence, harder to enumerate

### When to Use Long IDs
- **Legacy compatibility**: When integrating with existing systems
- **High-volume inserts**: When maximum insert performance is critical
- **Simple relationships**: For lookup tables or simple reference data

### Entity Design Pattern
```java
@Entity
@Table(name = "entity_name")
@Data
@EqualsAndHashCode(callSuper = true)
@FieldNameConstants
public class EntityName extends BaseEntityUUID {
    public static final String ENTITY_NAME = "ENTITY_NAME";
    
    @Column(name = "code", length = 64, nullable = false, updatable = false)
    private String code;
    
    @Column(name = "name", length = 100, nullable = false)
    private String name;
    
    // Other entity-specific fields...
}
```

## Audit Configuration

### Enable JPA Auditing
```java
@Configuration
@EnableJpaAuditing
public class JpaConfig {
    
    @Bean
    public AuditorAware<String> auditorProvider() {
        return new AuditorAwareImpl();
    }
}
```

### Custom Auditor Implementation
```java
@Component
public class AuditorAwareImpl implements AuditorAware<String> {
    
    @Override
    public Optional<String> getCurrentAuditor() {
        // Get current user from security context
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        
        if (authentication == null || !authentication.isAuthenticated()) {
            return Optional.of("system");
        }
        
        return Optional.of(authentication.getName());
    }
}
```

## Soft Delete Pattern

### Implementation
```java
// In service layer
@Override
@Transactional
public void delete(String id) {
    log.info("Deleting Entity with id: {}", id);
    
    Entity existing = repository.findById(id)
            .orElseThrow(() -> {
                log.warn("Entity not found with id: {}", id);
                return new ResourceNotFoundException("Entity", "id", id);
            });
    
    existing.setActive(false);
    saveAndPublishChangeEvent(existing);
    
    log.info("Entity deleted successfully with id: {}", id);
}
```

### Repository Queries
```java
// Find only active entities
@Query("SELECT e FROM Entity e WHERE e.active = true")
List<Entity> findAllActive();

// Include soft-deleted entities when needed
@Query("SELECT e FROM Entity e WHERE e.active = true OR e.active = false")
List<Entity> findAllIncludingDeleted();
```

## Field Name Constants

### Usage with @FieldNameConstants
```java
@Entity
@FieldNameConstants
public class MiniApp extends BaseEntityUUID {
    private String code;
    private String name;
    // ...
}

// Usage in specifications or queries
criteriaBuilder.equal(root.get(MiniApp.Fields.code), filterValue);

// Usage in mappers
@Mapping(target = MiniApp.Fields.id, ignore = true)
MiniApp toEntity(MiniAppDto dto);
```

## Testing ID Generation

### Unit Test Pattern
```java
@Test
void entity_creation_generatesUUIDv7() {
    // Given
    MiniApp entity = new MiniApp();
    entity.setCode("test-code");
    entity.setName("Test Name");
    
    // When
    MiniApp saved = repository.save(entity);
    
    // Then
    assertNotNull(saved.getId());
    assertTrue(saved.getId().matches("[0-9a-f]{8}-[0-9a-f]{4}-7[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}"));
    assertNotNull(saved.getCreatedOn());
    assertNotNull(saved.getUpdatedOn());
    assertEquals(0, saved.getVersion());
    assertTrue(saved.getActive());
}
```

## Migration Considerations

### From UUIDv4 to UUIDv7
1. **New entities**: Use UUIDv7 for all new entities
2. **Existing entities**: Consider migration impact on foreign keys
3. **Database indexes**: UUIDv7 provides better index performance
4. **Application compatibility**: Ensure all UUID handling code is compatible

### Database Schema
```sql
-- PostgreSQL example
CREATE TABLE miniapps (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(64) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    version INTEGER NOT NULL DEFAULT 0,
    created_by VARCHAR(255),
    updated_by VARCHAR(255),
    created_on TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_on TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    active BOOLEAN DEFAULT true
);

-- Index for soft delete queries
CREATE INDEX idx_miniapps_active ON miniapps(active);
```
