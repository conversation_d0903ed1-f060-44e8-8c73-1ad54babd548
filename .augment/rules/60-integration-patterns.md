---
type: "always_apply"
---

# Integration Patterns

## AWS Integration

### KMS (Key Management Service)
Used for encrypting/decrypting sensitive data like secrets and keys.

```java
@Service
@RequiredArgsConstructor
public class AwsKmsServiceImpl implements KmsService {
    private final KmsClient kmsClient;
    
    @Value("${aws.kms.key-id}")
    private String kmsKeyId;
    
    @Override
    public EncryptedSecret encrypt(String plaintext) {
        // Implementation for encrypting data using KMS
    }
    
    @Override
    public String decrypt(EncryptedSecret encryptedSecret) {
        // Implementation for decrypting data using KMS
    }
}
```

### S3 Integration
For file storage and retrieval.

```java
@Configuration
public class AwsClientsConfig {
    
    @Value("${aws.region}")
    private String awsRegion;
    
    @Value("${aws.endpoint:}")
    private String awsEndpoint;
    
    @Bean
    public S3Client s3Client() {
        S3ClientBuilder builder = S3Client.builder()
                .region(Region.of(awsRegion));
        
        if (!awsEndpoint.isEmpty()) {
            builder.endpointOverride(URI.create(awsEndpoint));
        }
        
        return builder.build();
    }
    
    @Bean
    public KmsClient kmsClient() {
        KmsClientBuilder builder = KmsClient.builder()
                .region(Region.of(awsRegion));
        
        if (!awsEndpoint.isEmpty()) {
            builder.endpointOverride(URI.create(awsEndpoint));
        }
        
        return builder.build();
    }
}
```

## Kafka Integration

### Configuration
```java
@Configuration
@EnableKafka
public class KafkaConfig {
    
    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;
    
    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        return new DefaultKafkaProducerFactory<>(configProps);
    }
    
    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }
}
```

### Consumer Pattern
```java
@Component
@Slf4j
@RequiredArgsConstructor
public class CommonKafkaConsumer {
    
    @KafkaListener(topics = "${kafka.topic.example}")
    public void handleMessage(String message) {
        log.info("Received Kafka message: {}", message);
        // Process message
    }
}
```

## Redis Integration (Redisson)

### Configuration
```java
@Configuration
public class RedissonConfig {
    @Value("${redisson.config.address}")
    private String redissonAddress;
    
    @Value("${redisson.config.password:}")
    private String redissonPassword;
    
    @Value("${redisson.config.connection.max:64}")
    private int connectionMaxPoolSize;
    
    @Value("${redisson.config.connection.min:24}")
    private int connectionCorePoolSize;
    
    @Value("${redisson.config.connection.timeout:3000}")
    private int connectionTimeoutMs;
    
    @Bean
    public RedissonClient redissonClient() {
        Config config = new Config();
        config.useSingleServer()
                .setConnectionPoolSize(connectionMaxPoolSize)
                .setConnectionMinimumIdleSize(connectionCorePoolSize)
                .setConnectTimeout(connectionTimeoutMs)
                .setKeepAlive(true)
                .setAddress(redissonAddress)
                .setPassword(redissonPassword.isEmpty() ? null : redissonPassword);
        return Redisson.create(config);
    }
}
```

### Pub/Sub Pattern
```java
@Component
@RequiredArgsConstructor
public class RedisPublisher {
    
    @Value("${redis.channel.change-event}")
    private String changeEventChannel;
    
    private final RedissonClient redissonClient;
    private final JsonUtil jsonUtil;
    private RTopic topic;
    
    @PostConstruct
    public void init() {
        topic = redissonClient.getTopic(changeEventChannel);
    }
    
    public void sendChangeEvent(String entityName, String id, String code) {
        var data = ChangeEvent.builder()
                .entity(entityName)
                .ids(Set.of(id))
                .codes(Set.of(code))
                .build();
        topic.publish(jsonUtil.toJson(data));
    }
}
```

```java
@Component
@Slf4j
@RequiredArgsConstructor
public class RedisListener {
    @Value("${redis.channel.change-event}")
    private String changeEventChannel;
    
    private final RedissonClient redissonClient;
    private final JsonUtil jsonUtil;
    private final MiniAppService miniAppService;
    
    @PostConstruct
    public void init() {
        redissonClient.getTopic(changeEventChannel).addListener(String.class, (channel, msg) -> {
            log.info("Received change event message: {}", msg);
            var data = jsonUtil.fromJson(msg, ChangeEvent.class);
            switch (data.entity()) {
                case MiniApp.ENTITY_NAME -> miniAppService.invalidateCache(data.ids(), data.codes());
                default -> log.warn("Unknown entity type: {}", data.entity());
            }
        });
    }
}
```

## Security Configuration

### Basic Security Setup
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            .csrf(csrf -> csrf.disable())
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/hello/**").permitAll()
                .anyRequest().authenticated()
            );
        return http.build();
    }
}
```

## REST Template Configuration

### HTTP Client Setup
```java
@Configuration
public class RestTemplateConfig {
    
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getInterceptors().add(new RestTemplateLoggingInterceptor());
        return restTemplate;
    }
}
```

### Logging Interceptor
```java
@Component
@Slf4j
public class RestTemplateLoggingInterceptor implements ClientHttpRequestInterceptor {
    
    @Override
    public ClientHttpResponse intercept(
            HttpRequest request,
            byte[] body,
            ClientHttpRequestExecution execution) throws IOException {
        
        log.debug("Request URI: {}", request.getURI());
        log.debug("Request Method: {}", request.getMethod());
        log.debug("Request Headers: {}", request.getHeaders());
        
        ClientHttpResponse response = execution.execute(request, body);
        
        log.debug("Response Status: {}", response.getStatusCode());
        log.debug("Response Headers: {}", response.getHeaders());
        
        return response;
    }
}
```

## Testing Integration Components

### Docker Test Containers (Maven)
The project uses Docker Maven plugin for integration testing:

```xml
<plugin>
    <groupId>io.fabric8</groupId>
    <artifactId>docker-maven-plugin</artifactId>
    <configuration>
        <images>
            <image>
                <alias>postgresql17_test</alias>
                <name>public.ecr.aws/docker/library/postgres:17.5</name>
                <!-- PostgreSQL configuration -->
            </image>
            <image>
                <name>docker.io/library/redis:7.4</name>
                <alias>redis_test</alias>
                <!-- Redis configuration -->
            </image>
            <image>
                <name>bitnami/kafka:3.5.1</name>
                <alias>kafka_test</alias>
                <!-- Kafka configuration -->
            </image>
            <image>
                <alias>localstack_aws</alias>
                <name>localstack/localstack:3.7</name>
                <!-- LocalStack for AWS services -->
            </image>
        </images>
    </configuration>
</plugin>
```

### Integration Test Patterns
- Use `@SpringBootTest` for full integration tests
- Use `@TestPropertySource` to override configuration for tests
- Use `@DirtiesContext` when tests modify shared state
- Mock external services using `@MockBean` when appropriate

## Best Practices

1. **Configuration**: Use `@Value` with default values for optional properties
2. **Connection Pooling**: Configure appropriate pool sizes for external services
3. **Timeouts**: Set reasonable timeouts for all external calls
4. **Error Handling**: Implement proper retry and circuit breaker patterns
5. **Monitoring**: Add metrics and health checks for all integrations
6. **Security**: Never log sensitive data like passwords or tokens
7. **Testing**: Use test containers or embedded services for integration tests
8. **Documentation**: Document all external dependencies and their configurations
